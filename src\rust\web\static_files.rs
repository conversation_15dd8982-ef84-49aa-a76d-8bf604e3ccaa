use axum::{
    response::{Html, Response},
    http::{StatusCode, header},
};
use std::path::Path;
use tokio::fs;

/// 服务主页
pub async fn serve_index() -> Result<Html<String>, StatusCode> {
    serve_html_file("dist/index.html").await
}

/// SPA回退处理
pub async fn serve_spa_fallback() -> Result<Html<String>, StatusCode> {
    // 对于SPA应用，所有未匹配的路由都返回index.html
    serve_html_file("dist/index.html").await
}

/// 服务HTML文件
async fn serve_html_file(file_path: &str) -> Result<Html<String>, StatusCode> {
    match fs::read_to_string(file_path).await {
        Ok(content) => {
            // 替换Tauri相关的脚本和配置，使其适用于Web环境
            let web_content = adapt_html_for_web(content);
            Ok(Html(web_content))
        }
        Err(_) => {
            // 如果找不到构建的文件，返回一个基本的HTML页面
            Ok(Html(create_fallback_html()))
        }
    }
}

/// 将HTML内容适配为Web环境
fn adapt_html_for_web(mut content: String) -> String {
    // 移除Tauri相关的脚本
    content = content.replace(r#"<script type="module" src="/__tauri__/index.js"></script>"#, "");
    
    // 添加Web环境的配置
    let web_config = r#"
    <script>
        // Web环境配置
        window.__WEB_MODE__ = true;
        window.__API_BASE_URL__ = '';
        
        // 模拟Tauri API
        window.__TAURI__ = {
            core: {
                invoke: async function(command, args) {
                    const response = await fetch(`/api/${command.replace(/_/g, '-')}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(args || {})
                    });
                    const result = await response.json();
                    if (result.success) {
                        return result.data;
                    } else {
                        throw new Error(result.error || 'API调用失败');
                    }
                }
            },
            event: {
                listen: function(event, handler) {
                    // Web环境下的事件监听实现
                    console.log('监听事件:', event);
                    return Promise.resolve(() => {});
                },
                emit: function(event, payload) {
                    // Web环境下的事件发送实现
                    console.log('发送事件:', event, payload);
                    return Promise.resolve();
                }
            }
        };
    </script>
    "#;
    
    // 在</head>标签前插入Web配置
    content = content.replace("</head>", &format!("{}</head>", web_config));
    
    content
}

/// 创建回退HTML页面
fn create_fallback_html() -> String {
    r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>寸止 Web</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            text-align: center;
            max-width: 600px;
        }
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .title {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #4a9eff;
        }
        .message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        .status {
            padding: 10px 20px;
            background: #2a2a2a;
            border-radius: 8px;
            border-left: 4px solid #4a9eff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🛑</div>
        <h1 class="title">寸止 Web</h1>
        <p class="message">
            Web服务器已启动，但前端资源尚未构建。<br>
            请运行以下命令构建前端资源：
        </p>
        <div class="status">
            <code>pnpm build</code>
        </div>
        <p class="message">
            构建完成后刷新页面即可使用完整的Web界面。
        </p>
    </div>
</body>
</html>"#.to_string()
}
