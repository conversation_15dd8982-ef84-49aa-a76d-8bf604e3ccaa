use axum::{
    extract::{<PERSON>, <PERSON><PERSON>},
    response::<PERSON><PERSON> as Response<PERSON><PERSON>,
    http::StatusCode,
};
use serde::{Deserialize, Serialize};
use crate::{
    config::{AppState, save_config, load_config},
    mcp::types::{PopupRequest, build_send_response},
    ui::AudioController,
    log_important,
};
use std::sync::atomic::AtomicBool;
use std::sync::Arc;

// API响应类型
#[derive(Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }
    
    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
        }
    }
}

// 请求类型
#[derive(Deserialize)]
pub struct ThemeRequest {
    pub theme: String,
}

#[derive(Deserialize)]
pub struct McpResponseRequest {
    pub user_input: Option<String>,
    pub selected_options: Vec<String>,
    pub images: Vec<ImageData>,
}

#[derive(Deserialize)]
pub struct ImageData {
    pub data: String,
    pub media_type: String,
    pub filename: Option<String>,
}

/// 获取应用信息
pub async fn get_app_info() -> Result<ResponseJson<ApiResponse<String>>, StatusCode> {
    let info = format!("寸止 Web v{}", env!("CARGO_PKG_VERSION"));
    Ok(ResponseJson(ApiResponse::success(info)))
}

/// 获取配置
pub async fn get_config(
    State(state): State<AppState>
) -> Result<ResponseJson<ApiResponse<serde_json::Value>>, StatusCode> {
    match state.config.lock() {
        Ok(config) => {
            match serde_json::to_value(&*config) {
                Ok(value) => Ok(ResponseJson(ApiResponse::success(value))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(format!("序列化配置失败: {}", e)))),
            }
        }
        Err(e) => Ok(ResponseJson(ApiResponse::error(format!("获取配置失败: {}", e)))),
    }
}

/// 更新配置
pub async fn update_config(
    State(state): State<AppState>,
    Json(config_data): Json<serde_json::Value>
) -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    // 这里可以根据需要实现配置更新逻辑
    Ok(ResponseJson(ApiResponse::success(())))
}

/// 获取主题
pub async fn get_theme(
    State(state): State<AppState>
) -> Result<ResponseJson<ApiResponse<String>>, StatusCode> {
    match state.config.lock() {
        Ok(config) => Ok(ResponseJson(ApiResponse::success(config.ui_config.theme.clone()))),
        Err(e) => Ok(ResponseJson(ApiResponse::error(format!("获取主题失败: {}", e)))),
    }
}

/// 设置主题
pub async fn set_theme(
    State(state): State<AppState>,
    Json(request): Json<ThemeRequest>
) -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    match state.config.lock() {
        Ok(mut config) => {
            config.ui_config.theme = request.theme;
            match save_config(&config) {
                Ok(_) => Ok(ResponseJson(ApiResponse::success(()))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(format!("保存主题失败: {}", e)))),
            }
        }
        Err(e) => Ok(ResponseJson(ApiResponse::error(format!("设置主题失败: {}", e)))),
    }
}

/// 处理MCP请求
pub async fn handle_mcp_request(
    Json(request): Json<PopupRequest>
) -> Result<ResponseJson<ApiResponse<String>>, StatusCode> {
    // 这里应该实现MCP请求的处理逻辑
    // 暂时返回成功响应
    Ok(ResponseJson(ApiResponse::success("MCP请求已接收".to_string())))
}

/// 发送MCP响应
pub async fn send_mcp_response(
    Json(request): Json<McpResponseRequest>
) -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    // 构建响应数据
    let images = request.images.into_iter().map(|img| {
        crate::mcp::types::ImageAttachment {
            data: img.data,
            media_type: img.media_type,
            filename: img.filename,
        }
    }).collect();
    
    let response = build_send_response(
        request.user_input,
        request.selected_options,
        images,
    );
    
    // 这里应该发送响应到MCP客户端
    log_important!(info, "收到MCP响应: {:?}", response);
    
    Ok(ResponseJson(ApiResponse::success(())))
}

/// 获取Telegram配置
pub async fn get_telegram_config(
    State(state): State<AppState>
) -> Result<ResponseJson<ApiResponse<serde_json::Value>>, StatusCode> {
    match state.config.lock() {
        Ok(config) => {
            match serde_json::to_value(&config.telegram_config) {
                Ok(value) => Ok(ResponseJson(ApiResponse::success(value))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(format!("序列化Telegram配置失败: {}", e)))),
            }
        }
        Err(e) => Ok(ResponseJson(ApiResponse::error(format!("获取Telegram配置失败: {}", e)))),
    }
}

/// 设置Telegram配置
pub async fn set_telegram_config(
    State(state): State<AppState>,
    Json(telegram_config): Json<serde_json::Value>
) -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    // 这里可以根据需要实现Telegram配置更新逻辑
    Ok(ResponseJson(ApiResponse::success(())))
}

/// 获取音频通知设置
pub async fn get_audio_notification(
    State(state): State<AppState>
) -> Result<ResponseJson<ApiResponse<bool>>, StatusCode> {
    match state.config.lock() {
        Ok(config) => Ok(ResponseJson(ApiResponse::success(config.audio_config.enabled))),
        Err(e) => Ok(ResponseJson(ApiResponse::error(format!("获取音频设置失败: {}", e)))),
    }
}

/// 设置音频通知
pub async fn set_audio_notification(
    State(state): State<AppState>,
    Json(enabled): Json<bool>
) -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    match state.config.lock() {
        Ok(mut config) => {
            config.audio_config.enabled = enabled;
            match save_config(&config) {
                Ok(_) => Ok(ResponseJson(ApiResponse::success(()))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(format!("保存音频设置失败: {}", e)))),
            }
        }
        Err(e) => Ok(ResponseJson(ApiResponse::error(format!("设置音频通知失败: {}", e)))),
    }
}

/// 播放通知音频
pub async fn play_notification_sound() -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    // 这里应该实现音频播放逻辑
    // 由于Web环境的限制，可能需要通过前端来播放音频
    Ok(ResponseJson(ApiResponse::success(())))
}

/// 测试音频
pub async fn test_audio_sound() -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    // 这里应该实现测试音频播放逻辑
    Ok(ResponseJson(ApiResponse::success(())))
}

/// 停止音频
pub async fn stop_audio_sound() -> Result<ResponseJson<ApiResponse<()>>, StatusCode> {
    // 这里应该实现停止音频播放逻辑
    Ok(ResponseJson(ApiResponse::success(())))
}
