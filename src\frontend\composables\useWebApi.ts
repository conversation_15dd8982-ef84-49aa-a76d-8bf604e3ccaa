// Web环境API适配层
import { ref, computed } from 'vue'

// 检测是否为Web环境
export const isWebMode = computed(() => {
  return typeof window !== 'undefined' && (window as any).__WEB_MODE__ === true
})

// API基础URL
const API_BASE_URL = computed(() => {
  if (typeof window !== 'undefined') {
    return (window as any).__API_BASE_URL__ || ''
  }
  return ''
})

// HTTP请求封装
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL.value}/api/${endpoint}`
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
  }
  
  const response = await fetch(url, {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  })
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
  
  const result = await response.json()
  
  if (!result.success) {
    throw new Error(result.error || 'API调用失败')
  }
  
  return result.data
}

// Web API适配器
export const webApi = {
  // 应用信息
  async getAppInfo(): Promise<string> {
    return apiRequest('app-info')
  },
  
  // 配置管理
  async getConfig(): Promise<any> {
    return apiRequest('config')
  },
  
  async updateConfig(config: any): Promise<void> {
    return apiRequest('config', {
      method: 'POST',
      body: JSON.stringify(config),
    })
  },
  
  // 主题管理
  async getTheme(): Promise<string> {
    return apiRequest('theme')
  },
  
  async setTheme(theme: string): Promise<void> {
    return apiRequest('theme', {
      method: 'POST',
      body: JSON.stringify({ theme }),
    })
  },
  
  // MCP请求处理
  async handleMcpRequest(request: any): Promise<string> {
    return apiRequest('mcp-request', {
      method: 'POST',
      body: JSON.stringify(request),
    })
  },
  
  async sendMcpResponse(response: any): Promise<void> {
    return apiRequest('mcp-response', {
      method: 'POST',
      body: JSON.stringify(response),
    })
  },
  
  // Telegram配置
  async getTelegramConfig(): Promise<any> {
    return apiRequest('telegram-config')
  },
  
  async setTelegramConfig(config: any): Promise<void> {
    return apiRequest('telegram-config', {
      method: 'POST',
      body: JSON.stringify(config),
    })
  },
  
  // 音频通知
  async getAudioNotification(): Promise<boolean> {
    return apiRequest('audio-notification')
  },
  
  async setAudioNotification(enabled: boolean): Promise<void> {
    return apiRequest('audio-notification', {
      method: 'POST',
      body: JSON.stringify(enabled),
    })
  },
  
  async playNotificationSound(): Promise<void> {
    return apiRequest('play-audio', { method: 'POST' })
  },
  
  async testAudioSound(): Promise<void> {
    return apiRequest('test-audio', { method: 'POST' })
  },
  
  async stopAudioSound(): Promise<void> {
    return apiRequest('stop-audio', { method: 'POST' })
  },
}

// 统一的API调用接口，自动适配Tauri和Web环境
export function useApi() {
  const invoke = async (command: string, args?: any) => {
    if (isWebMode.value) {
      // Web环境：使用HTTP API
      const endpoint = command.replace(/_/g, '-')

      // 根据命令类型决定HTTP方法
      const isGetCommand = command.startsWith('get_') ||
                          ['get_app_info', 'get_theme', 'get_config'].includes(command)

      if (isGetCommand && (!args || Object.keys(args).length === 0)) {
        return apiRequest(endpoint, { method: 'GET' })
      } else {
        return apiRequest(endpoint, {
          method: 'POST',
          body: JSON.stringify(args || {}),
        })
      }
    } else {
      // Tauri环境：使用原有的invoke
      const { invoke: tauriInvoke } = await import('@tauri-apps/api/core')
      return tauriInvoke(command, args)
    }
  }

  return {
    invoke,
    isWebMode,
  }
}
