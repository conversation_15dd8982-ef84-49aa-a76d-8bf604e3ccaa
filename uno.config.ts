import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  presetWebFonts,
  presetWind3,
} from 'unocss'
import { semanticColors } from './src/frontend/theme/colors'

export default defineConfig({
  presets: [
    presetTypography(),
    presetWind3(),
    presetAttributify(),
    presetIcons({
      collections: {
        carbon: () => import('@iconify-json/carbon/icons.json').then(i => i.default),
      },
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    presetWebFonts({
      fonts: {
        sans: 'Inter:400,500,600,700',
        mono: 'JetBrains Mono:400,500,600',
      },
    }),
  ],
  theme: {
    colors: {
      // 语义化颜色系统 - 重新定义基础颜色让它们适配主题
      ...semanticColors,
      // 添加表面色的直接映射
      surface: 'var(--color-surface)',
    },
    fontSize: {
      'xs': 'var(--font-size-xs, 0.75rem)',
      'sm': 'var(--font-size-sm, 0.875rem)',
      'base': 'var(--font-size-base, 0.875rem)',
      'lg': 'var(--font-size-lg, 1rem)',
      'xl': 'var(--font-size-xl, 1.125rem)',
      '2xl': 'var(--font-size-2xl, 1.25rem)',
      '3xl': 'var(--font-size-3xl, 1.5rem)',
    },
    fontFamily: {
      sans: 'var(--font-family, Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif)',
      mono: ['JetBrains Mono', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'Consolas', 'Courier New', 'monospace'],
      custom: 'var(--font-family)',
    },
    spacing: {
      18: '4.5rem',
      88: '22rem',
    },
  },
  shortcuts: [
  ],
  rules: [
    // 自定义规则
    [/^animate-in$/, () => ({ animation: 'fadeIn 0.2s ease-in-out' })],
    [/^fade-in$/, () => ({ opacity: '1' })],
  ],
  safelist: [
    // 语义化颜色类名 - 基于新的颜色系统
    'bg-surface',
    'text-surface',
    'border-surface',
    'bg-black',
    'bg-black-50',
    'bg-black-100',
    'bg-black-200',
    'bg-black-300',
    'bg-black-400',
    'bg-black-500',
    'bg-black-600',
    'bg-black-700',
    'bg-black-800',
    'bg-black-900',
    'bg-black-950',
    'bg-white',
    'text-black',
    'text-black-50',
    'text-black-100',
    'text-black-200',
    'text-black-300',
    'text-black-400',
    'text-black-500',
    'text-black-600',
    'text-black-700',
    'text-black-800',
    'text-black-900',
    'text-black-950',
    'text-white',
    'border-black',
    'border-black-50',
    'border-black-100',
    'border-black-200',
    'border-black-300',
    'border-black-400',
    'border-black-500',
    'border-black-600',
    'border-black-700',
    'border-black-800',
    'border-black-900',
    'border-black-950',
    'border-white',
    // 灰度色阶
    'bg-gray-50',
    'bg-gray-100',
    'bg-gray-200',
    'bg-gray-300',
    'bg-gray-400',
    'bg-gray-500',
    'bg-gray-600',
    'bg-gray-700',
    'bg-gray-800',
    'bg-gray-900',
    'bg-gray-950',
    'text-gray-50',
    'text-gray-100',
    'text-gray-200',
    'text-gray-300',
    'text-gray-400',
    'text-gray-500',
    'text-gray-600',
    'text-gray-700',
    'text-gray-800',
    'text-gray-900',
    'text-gray-950',
    'border-gray-50',
    'border-gray-100',
    'border-gray-200',
    'border-gray-300',
    'border-gray-400',
    'border-gray-500',
    'border-gray-600',
    'border-gray-700',
    'border-gray-800',
    'border-gray-900',
    'border-gray-950',
    // 主色调
    'bg-primary-50',
    'bg-primary-100',
    'bg-primary-200',
    'bg-primary-300',
    'bg-primary-400',
    'bg-primary-500',
    'bg-primary-600',
    'bg-primary-700',
    'bg-primary-800',
    'bg-primary-900',
    'bg-primary-950',
    'text-primary-500',
    'text-primary-600',
    'text-primary-700',
    'border-primary-200',
    'border-primary-500',
    // 功能色
    'bg-success',
    'bg-warning',
    'bg-error',
    'bg-info',
    'text-success',
    'text-warning',
    'text-error',
    'text-info',
    // 动画
    'animate-pulse',
    // 滚动条样式类
    'scrollbar-thin',
    'scrollbar-primary',
    'scrollbar-hidden',
    'scrollbar-thick',
    'scrollbar-code',
    // 图标类
    'i-carbon-settings-services',
    'i-carbon-data-base',
    'i-carbon-color-palette',
    'i-carbon-settings',
    'i-carbon-repeat',
    'i-carbon-document',
    'i-carbon-copy',
    'i-carbon-checkmark',
    'i-carbon-checkmark-filled',
    'i-carbon-rule',
    'i-carbon-chat',
    'i-carbon-information',
    'i-carbon-sun',
    'i-carbon-moon',
    'i-carbon-play',
    'i-carbon-close',
    'i-carbon-send',
    'i-carbon-warning',
    'i-carbon-volume-up',
    'i-carbon-arrow-left',
    'i-carbon-favorite',
    'i-carbon-logo-github',
    'i-carbon-star',
    'i-carbon-upgrade',
    'i-carbon-renew',
    'i-carbon-download',
    'i-carbon-pin',
    'i-carbon-pin-filled',
  ],
})
