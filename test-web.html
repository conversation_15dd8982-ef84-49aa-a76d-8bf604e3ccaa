<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>寸止 Web UI 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }
        .title {
            font-size: 2rem;
            color: #4a9eff;
            margin-bottom: 0.5rem;
        }
        .subtitle {
            opacity: 0.7;
            font-size: 1.1rem;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #4a9eff;
        }
        .test-section h3 {
            margin-top: 0;
            color: #4a9eff;
        }
        .test-button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #3a8eef;
        }
        .test-result {
            background: #333;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            color: #4ade80;
        }
        .error {
            color: #f87171;
        }
        .info {
            color: #60a5fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛑</div>
            <h1 class="title">寸止 Web UI</h1>
            <p class="subtitle">Web服务器功能测试页面</p>
        </div>

        <div class="test-section">
            <h3>🔧 基础API测试</h3>
            <button class="test-button" onclick="testAppInfo()">获取应用信息</button>
            <button class="test-button" onclick="testTheme()">获取主题</button>
            <button class="test-button" onclick="testConfig()">获取配置</button>
            <div id="basic-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🎨 主题测试</h3>
            <button class="test-button" onclick="setTheme('dark')">设置暗色主题</button>
            <button class="test-button" onclick="setTheme('light')">设置亮色主题</button>
            <div id="theme-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔊 音频测试</h3>
            <button class="test-button" onclick="testAudioStatus()">获取音频状态</button>
            <button class="test-button" onclick="toggleAudio()">切换音频通知</button>
            <button class="test-button" onclick="playTestAudio()">播放测试音频</button>
            <div id="audio-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>📱 Telegram测试</h3>
            <button class="test-button" onclick="testTelegramConfig()">获取Telegram配置</button>
            <div id="telegram-result" class="test-result"></div>
        </div>
    </div>

    <script>
        // 测试工具函数
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`/api/${endpoint}`, options);
                const result = await response.json();
                
                return {
                    success: response.ok && result.success,
                    data: result.data,
                    error: result.error || (response.ok ? null : `HTTP ${response.status}`)
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        function displayResult(elementId, result, operation) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            
            let output = `[${timestamp}] ${operation}\n`;
            
            if (result.success) {
                output += `✅ 成功: ${JSON.stringify(result.data, null, 2)}`;
                element.innerHTML += `<span class="success">${output}</span>\n\n`;
            } else {
                output += `❌ 失败: ${result.error}`;
                element.innerHTML += `<span class="error">${output}</span>\n\n`;
            }
            
            element.scrollTop = element.scrollHeight;
        }

        // 基础API测试
        async function testAppInfo() {
            const result = await apiCall('get-app-info');
            displayResult('basic-result', result, '获取应用信息');
        }

        async function testTheme() {
            const result = await apiCall('get-theme');
            displayResult('basic-result', result, '获取主题');
        }

        async function testConfig() {
            const result = await apiCall('get-config');
            displayResult('basic-result', result, '获取配置');
        }

        // 主题测试
        async function setTheme(theme) {
            const result = await apiCall('set-theme', 'POST', { theme });
            displayResult('theme-result', result, `设置主题为 ${theme}`);
        }

        // 音频测试
        async function testAudioStatus() {
            const result = await apiCall('get-audio-notification-enabled');
            displayResult('audio-result', result, '获取音频通知状态');
        }

        async function toggleAudio() {
            // 先获取当前状态
            const currentResult = await apiCall('get-audio-notification-enabled');
            if (currentResult.success) {
                const newState = !currentResult.data;
                const result = await apiCall('set-audio-notification-enabled', 'POST', newState);
                displayResult('audio-result', result, `设置音频通知为 ${newState ? '启用' : '禁用'}`);
            } else {
                displayResult('audio-result', currentResult, '获取音频状态失败');
            }
        }

        async function playTestAudio() {
            const result = await apiCall('test-audio-sound', 'POST');
            displayResult('audio-result', result, '播放测试音频');
        }

        // Telegram测试
        async function testTelegramConfig() {
            const result = await apiCall('get-telegram-config');
            displayResult('telegram-result', result, '获取Telegram配置');
        }

        // 页面加载时显示欢迎信息
        window.onload = function() {
            const basicResult = document.getElementById('basic-result');
            basicResult.innerHTML = '<span class="info">点击上方按钮开始测试API功能...</span>\n';
        };
    </script>
</body>
</html>
