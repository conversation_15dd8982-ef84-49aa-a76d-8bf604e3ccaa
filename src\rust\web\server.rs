use axum::{
    routing::{get, post},
    Router,
    response::Html,
    http::StatusCode,
};
use tower::ServiceBuilder;
use tower_http::{
    cors::{CorsLayer, Any},
    services::ServeDir,
};
use std::net::SocketAddr;
use crate::{log_important, config::AppState};
use super::{api, static_files};

/// 启动Web服务器
pub async fn run_web_server(host: &str, port: u16) -> Result<(), Box<dyn std::error::Error>> {
    // 创建应用状态
    let app_state = AppState::default();
    
    // 构建路由
    let app = create_app_router(app_state);
    
    // 解析监听地址
    let addr: SocketAddr = format!("{}:{}", host, port).parse()?;
    
    log_important!(info, "Web服务器启动在 http://{}", addr);
    
    // 启动服务器
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;
    
    Ok(())
}

/// 创建应用路由
fn create_app_router(app_state: AppState) -> Router {
    Router::new()
        // API路由 - 映射Tauri命令到HTTP端点
        .route("/api/get-app-info", get(api::get_app_info))
        .route("/api/get-config", get(api::get_config))
        .route("/api/update-config", post(api::update_config))
        .route("/api/get-theme", get(api::get_theme))
        .route("/api/set-theme", post(api::set_theme))
        .route("/api/handle-mcp-request", post(api::handle_mcp_request))
        .route("/api/send-mcp-response", post(api::send_mcp_response))
        .route("/api/get-telegram-config", get(api::get_telegram_config))
        .route("/api/set-telegram-config", post(api::set_telegram_config))
        .route("/api/get-audio-notification-enabled", get(api::get_audio_notification))
        .route("/api/set-audio-notification-enabled", post(api::set_audio_notification))
        .route("/api/play-notification-sound", post(api::play_notification_sound))
        .route("/api/test-audio-sound", post(api::test_audio_sound))
        .route("/api/stop-audio-sound", post(api::stop_audio_sound))
        
        // 静态文件服务
        .route("/", get(static_files::serve_index))
        .nest_service("/assets", ServeDir::new("dist/assets"))
        .fallback(static_files::serve_spa_fallback)
        
        // 中间件
        .layer(
            ServiceBuilder::new()
                .layer(
                    CorsLayer::new()
                        .allow_origin(Any)
                        .allow_methods(Any)
                        .allow_headers(Any)
                )
        )
        .with_state(app_state)
}
