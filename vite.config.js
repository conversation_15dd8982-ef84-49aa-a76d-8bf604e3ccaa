import process from 'node:process'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import { defineConfig } from 'vite'

export default defineConfig(({ mode }) => ({
  plugins: [
    vue(),
    UnoCSS(),
  ],
  clearScreen: false,
  // 根据模式设置基础路径
  base: mode === 'web' ? '/' : './',
  server: {
    port: 5176,
    strictPort: true,
    host: '0.0.0.0',
    hmr: {
      port: 5177,
    },
  },
  envPrefix: ['VITE_', 'TAURI_'],
  define: {
    // 在Web模式下定义环境变量
    __WEB_MODE__: mode === 'web',
  },
  build: {
    target: mode === 'web' ? 'es2020' : (process.env.TAURI_PLATFORM === 'windows' ? 'chrome105' : 'safari13'),
    minify: mode === 'web' ? 'esbuild' : (!process.env.TAURI_DEBUG ? 'esbuild' : false),
    sourcemap: mode === 'web' ? false : !!process.env.TAURI_DEBUG,
    chunkSizeWarningLimit: 1500,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', '@vueuse/core'],
          markdown: ['markdown-it', 'highlight.js'],
        },
      },
    },
  },
}))
